import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Calendar, Clock, ArrowLeft } from "lucide-react"
import Image from "next/image"

export default function NewsSection() {
  const news = [
    {
      id: 1,
      title: "بدء العام الدراسي الجديد 2024-2025",
      excerpt: "نرحب بجميع الطلاب والطالبات في العام الدراسي الجديد مع تطبيق أحدث المناهج التعليمية",
      date: "2024-09-01",
      time: "10:00 ص",
      image: "/placeholder.svg?height=200&width=300",
      category: "إعلانات",
    },
    {
      id: 2,
      title: "فوز طلابنا في مسابقة العلوم الوطنية",
      excerpt: "حقق طلاب المدرسة المركز الأول في مسابقة العلوم على مستوى المحافظة",
      date: "2024-08-25",
      time: "2:30 م",
      image: "/placeholder.svg?height=200&width=300",
      category: "إنجازات",
    },
    {
      id: 3,
      title: "ورشة تدريبية للمعلمين حول التعلم الرقمي",
      excerpt: "تم تنظيم ورشة تدريبية متخصصة لتطوير مهارات المعلمين في استخدام التكنولوجيا",
      date: "2024-08-20",
      time: "9:00 ص",
      image: "/placeholder.svg?height=200&width=300",
      category: "تطوير",
    },
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">أحدث الأخبار</h2>
          <p className="text-xl text-gray-600">تابع آخر أخبار وأنشطة المدرسة</p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {news.map((item) => (
            <Card
              key={item.id}
              className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden"
            >
              <div className="relative overflow-hidden">
                <Image
                  src={item.image || "/placeholder.svg"}
                  alt={item.title}
                  width={300}
                  height={200}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4">
                  <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {item.category}
                  </span>
                </div>
              </div>

              <CardContent className="p-6">
                <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(item.date).toLocaleDateString("ar-EG")}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>{item.time}</span>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                  {item.title}
                </h3>

                <p className="text-gray-600 mb-4 leading-relaxed">{item.excerpt}</p>

                <Button variant="ghost" className="p-0 h-auto font-semibold text-blue-600 hover:text-blue-700">
                  اقرأ المزيد
                  <ArrowLeft className="w-4 h-4 mr-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
            عرض جميع الأخبار
          </Button>
        </div>
      </div>
    </section>
  )
}
