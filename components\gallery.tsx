"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Play, ImageIcon } from "lucide-react"
import Image from "next/image"

export default function Gallery() {
  const [activeTab, setActiveTab] = useState("photos")

  const photos = [
    {
      id: 1,
      src: "/placeholder.svg?height=300&width=400",
      title: "الفصول الدراسية الحديثة",
      category: "مرافق",
    },
    {
      id: 2,
      src: "/placeholder.svg?height=300&width=400",
      title: "مختبر العلوم المتطور",
      category: "مرافق",
    },
    {
      id: 3,
      src: "/placeholder.svg?height=300&width=400",
      title: "نشاط رياضي للطلاب",
      category: "أنشطة",
    },
    {
      id: 4,
      src: "/placeholder.svg?height=300&width=400",
      title: "حفل التخرج السنوي",
      category: "فعاليات",
    },
    {
      id: 5,
      src: "/placeholder.svg?height=300&width=400",
      title: "ورشة تعليمية",
      category: "أنشطة",
    },
    {
      id: 6,
      src: "/placeholder.svg?height=300&width=400",
      title: "المكتبة المدرسية",
      category: "مرافق",
    },
  ]

  const videos = [
    {
      id: 1,
      thumbnail: "/placeholder.svg?height=200&width=300",
      title: "جولة في المدرسة",
      duration: "3:45",
    },
    {
      id: 2,
      thumbnail: "/placeholder.svg?height=200&width=300",
      title: "أنشطة الطلاب",
      duration: "5:20",
    },
    {
      id: 3,
      thumbnail: "/placeholder.svg?height=200&width=300",
      title: "حفل التخرج 2024",
      duration: "8:15",
    },
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">معرض الصور والفيديوهات</h2>
          <p className="text-xl text-gray-600">شاهد لحظات مميزة من حياة المدرسة</p>
        </div>

        {/* Tabs */}
        <div className="flex justify-center mb-12">
          <div className="bg-gray-100 rounded-lg p-1 flex">
            <Button
              variant={activeTab === "photos" ? "default" : "ghost"}
              onClick={() => setActiveTab("photos")}
              className="flex items-center gap-2"
            >
              <ImageIcon className="w-4 h-4" />
              الصور
            </Button>
            <Button
              variant={activeTab === "videos" ? "default" : "ghost"}
              onClick={() => setActiveTab("videos")}
              className="flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              الفيديوهات
            </Button>
          </div>
        </div>

        {/* Photos Tab */}
        {activeTab === "photos" && (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {photos.map((photo) => (
              <Card
                key={photo.id}
                className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden"
              >
                <div className="relative overflow-hidden">
                  <Image
                    src={photo.src || "/placeholder.svg"}
                    alt={photo.title}
                    width={400}
                    height={300}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                    <Button size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      عرض الصورة
                    </Button>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {photo.category}
                    </span>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-gray-900">{photo.title}</h3>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Videos Tab */}
        {activeTab === "videos" && (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {videos.map((video) => (
              <Card
                key={video.id}
                className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden"
              >
                <div className="relative overflow-hidden">
                  <Image
                    src={video.thumbnail || "/placeholder.svg"}
                    alt={video.title}
                    width={300}
                    height={200}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                    <div className="bg-white rounded-full p-4 group-hover:scale-110 transition-transform duration-300">
                      <Play className="w-8 h-8 text-blue-600" />
                    </div>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <span className="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">
                      {video.duration}
                    </span>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-gray-900">{video.title}</h3>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </section>
  )
}
