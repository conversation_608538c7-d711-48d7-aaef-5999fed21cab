import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Calendar, Clock, MapPin, Users } from "lucide-react"

export default function EventsPage() {
  const upcomingEvents = [
    {
      id: 1,
      title: "معرض العلوم السنوي",
      description: "معرض يعرض مشاريع الطلاب العلمية والابتكارية في مختلف المجالات",
      date: "2024-12-15",
      time: "10:00 ص - 4:00 م",
      location: "قاعة المدرسة الكبرى",
      attendees: 200,
      category: "تعليمي",
      status: "قريباً",
    },
    {
      id: 2,
      title: "حفل التميز الأكاديمي",
      description: "تكريم الطلاب المتفوقين والمعلمين المتميزين في العام الدراسي",
      date: "2024-12-20",
      time: "6:00 م - 9:00 م",
      location: "المسرح المدرسي",
      attendees: 300,
      category: "احتفالي",
      status: "قريباً",
    },
    {
      id: 3,
      title: "ورشة الروبوتيك للطلاب",
      description: "ورشة تدريبية لتعليم الطلاب أساسيات الروبوتيك والبرمجة",
      date: "2024-12-10",
      time: "9:00 ص - 12:00 م",
      location: "مختبر الحاسوب",
      attendees: 50,
      category: "تقني",
      status: "التسجيل مفتوح",
    },
  ]

  const pastEvents = [
    {
      id: 4,
      title: "يوم الرياضة المدرسي",
      description: "يوم مليء بالأنشطة الرياضية والمسابقات بين الطلاب",
      date: "2024-11-15",
      time: "8:00 ص - 2:00 م",
      location: "الملعب المدرسي",
      attendees: 400,
      category: "رياضي",
      status: "انتهى",
    },
    {
      id: 5,
      title: "مهرجان الثقافة والفنون",
      description: "عروض فنية وثقافية متنوعة من إبداع طلاب المدرسة",
      date: "2024-11-01",
      time: "5:00 م - 8:00 م",
      location: "المسرح المدرسي",
      attendees: 250,
      category: "ثقافي",
      status: "انتهى",
    },
  ]

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      تعليمي: "bg-blue-500",
      احتفالي: "bg-purple-500",
      تقني: "bg-green-500",
      رياضي: "bg-orange-500",
      ثقافي: "bg-pink-500",
      "تطوير مهني": "bg-indigo-500",
    }
    return colors[category] || "bg-gray-500"
  }

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      قريباً: "bg-yellow-100 text-yellow-800",
      "التسجيل مفتوح": "bg-green-100 text-green-800",
      انتهى: "bg-gray-100 text-gray-800",
    }
    return colors[status] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="min-h-screen bg-white" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-lg sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-20">
            <Link href="/" className="flex items-center space-x-4 space-x-reverse">
              <div className="w-[60px] h-[60px] bg-blue-900 rounded-full flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white"
                >
                  <path d="M22 10v6M2 10l10-5 10 5-10 5z"></path>
                  <path d="M6 12v5c0 2 2 3 6 3s6-1 6-3v-5"></path>
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-blue-900">مدرسة الجيل الواعد الخاصة</h1>
                <p className="text-sm text-gray-600">نحو مستقبل أفضل</p>
              </div>
            </Link>

            <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
              <Link href="/" className="text-gray-700 hover:text-blue-600">
                الرئيسية
              </Link>
              <Link href="/#about" className="text-gray-700 hover:text-blue-600">
                من نحن
              </Link>
              <Link href="/#news" className="text-gray-700 hover:text-blue-600">
                الأخبار
              </Link>
              <Link href="/events" className="text-blue-600 font-medium">
                الفعاليات
              </Link>
              <Link href="/#contact" className="text-gray-700 hover:text-blue-600">
                تواصل معنا
              </Link>
            </nav>

            <Button variant="ghost" size="icon" className="lg:hidden">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            </Button>
          </div>
        </div>
      </header>

      <div className="py-20">
        <div className="container mx-auto px-4">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">فعاليات المدرسة</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              تابع جميع الفعاليات والأنشطة المدرسية التي تهدف إلى إثراء تجربة الطلاب التعليمية والثقافية
            </p>
          </div>

          {/* Upcoming Events */}
          <div className="mb-20">
            <div className="flex items-center gap-4 mb-12">
              <div className="bg-blue-500 rounded-full p-3">
                <Calendar className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">الفعاليات القادمة</h2>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {upcomingEvents.map((event) => (
                <Card
                  key={event.id}
                  className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden"
                >
                  <div className="relative overflow-hidden h-48 bg-gray-200 flex items-center justify-center">
                    <Calendar className="w-12 h-12 text-gray-400" />
                    <div className="absolute top-4 right-4 flex gap-2">
                      <span
                        className={`${getCategoryColor(event.category)} text-white px-3 py-1 rounded-full text-sm font-medium`}
                      >
                        {event.category}
                      </span>
                      <span className={`${getStatusColor(event.status)} px-3 py-1 rounded-full text-sm font-medium`}>
                        {event.status}
                      </span>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                      {event.title}
                    </h3>

                    <p className="text-gray-600 mb-4 leading-relaxed">{event.description}</p>

                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-3 text-sm text-gray-500">
                        <Calendar className="w-4 h-4" />
                        <span>{new Date(event.date).toLocaleDateString("ar-EG")}</span>
                      </div>
                      <div className="flex items-center gap-3 text-sm text-gray-500">
                        <Clock className="w-4 h-4" />
                        <span>{event.time}</span>
                      </div>
                      <div className="flex items-center gap-3 text-sm text-gray-500">
                        <MapPin className="w-4 h-4" />
                        <span>{event.location}</span>
                      </div>
                      <div className="flex items-center gap-3 text-sm text-gray-500">
                        <Users className="w-4 h-4" />
                        <span>{event.attendees} مشارك متوقع</span>
                      </div>
                    </div>

                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      {event.status === "التسجيل مفتوح" ? "سجل الآن" : "المزيد من التفاصيل"}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Past Events */}
          <div>
            <div className="flex items-center gap-4 mb-12">
              <div className="bg-gray-500 rounded-full p-3">
                <Calendar className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">الفعاليات السابقة</h2>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {pastEvents.map((event) => (
                <Card
                  key={event.id}
                  className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden opacity-90"
                >
                  <div className="relative overflow-hidden h-48 bg-gray-200 flex items-center justify-center">
                    <Calendar className="w-12 h-12 text-gray-400" />
                    <div className="absolute top-4 right-4 flex gap-2">
                      <span
                        className={`${getCategoryColor(event.category)} text-white px-3 py-1 rounded-full text-sm font-medium`}
                      >
                        {event.category}
                      </span>
                      <span className={`${getStatusColor(event.status)} px-3 py-1 rounded-full text-sm font-medium`}>
                        {event.status}
                      </span>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{event.title}</h3>

                    <p className="text-gray-600 mb-4 leading-relaxed">{event.description}</p>

                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-3 text-sm text-gray-500">
                        <Calendar className="w-4 h-4" />
                        <span>{new Date(event.date).toLocaleDateString("ar-EG")}</span>
                      </div>
                      <div className="flex items-center gap-3 text-sm text-gray-500">
                        <Clock className="w-4 h-4" />
                        <span>{event.time}</span>
                      </div>
                      <div className="flex items-center gap-3 text-sm text-gray-500">
                        <MapPin className="w-4 h-4" />
                        <span>{event.location}</span>
                      </div>
                      <div className="flex items-center gap-3 text-sm text-gray-500">
                        <Users className="w-4 h-4" />
                        <span>{event.attendees} مشارك</span>
                      </div>
                    </div>

                    <Button variant="outline" className="w-full">
                      عرض الصور والتقرير
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-gray-400 text-sm">© 2024 مدرسة الجيل الواعد الخاصة. جميع الحقوق محفوظة.</p>
            <div className="flex justify-center space-x-6 space-x-reverse mt-4">
              <Link href="/" className="text-gray-400 hover:text-white text-sm transition-colors">
                الرئيسية
              </Link>
              <Link href="/#about" className="text-gray-400 hover:text-white text-sm transition-colors">
                من نحن
              </Link>
              <Link href="/#contact" className="text-gray-400 hover:text-white text-sm transition-colors">
                تواصل معنا
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
