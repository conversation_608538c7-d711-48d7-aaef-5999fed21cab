import { Card, CardContent } from "@/components/ui/card"
import { Target, Heart, Star, Lightbulb } from "lucide-react"

export default function AboutSection() {
  const features = [
    {
      icon: Target,
      title: "رؤيتنا للطلاب",
      description: "تقديم بيئة تعليمية حديثة وتطوير مهارات التفكير والنقد مع الاهتمام بالأنشطة والابتكار",
      color: "bg-blue-500",
    },
    {
      icon: Heart,
      title: "رؤيتنا للمعلمين",
      description: "التميز في الأداء التربوي والتطوير المهني المستمر مع استخدام تقنيات حديثة في التدريس",
      color: "bg-green-500",
    },
    {
      icon: Star,
      title: "رؤيتنا لأولياء الأمور",
      description: "شراكة حقيقية في العملية التعليمية مع تواصل دائم وشفاف وإشراك في الأنشطة والقرارات",
      color: "bg-purple-500",
    },
    {
      icon: Lightbulb,
      title: "رؤيتنا للإدارة",
      description: "نظام إداري فعال ومرن مع شفافية في اتخاذ القرار وتطبيق الجودة الإدارية والتحول الرقمي",
      color: "bg-orange-500",
    },
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">من نحن</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            مدرسة الجيل الواعد الخاصة هي مؤسسة تعليمية رائدة تأسست بهدف تقديم تعليم متميز يواكب التطورات الحديثة ويعد
            جيلاً واعداً قادراً على بناء المستقبل
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
              <CardContent className="p-8 text-center">
                <div
                  className={`${feature.color} rounded-full p-4 w-20 h-20 mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                >
                  <feature.icon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 bg-white rounded-2xl p-8 shadow-lg">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">تاريخنا وإنجازاتنا</h3>
              <div className="space-y-4 text-gray-600">
                <p>
                  تأسست مدرسة الجيل الواعد الخاصة عام 2008 برؤية طموحة لتقديم تعليم متميز يجمع بين الأصالة والحداثة.
                </p>
                <p>
                  على مدار أكثر من 15 عاماً، نجحت المدرسة في تخريج مئات الطلاب المتميزين الذين يشغلون مناصب مهمة في مختلف
                  المجالات.
                </p>
                <p>
                  حصلت المدرسة على العديد من الجوائز والشهادات التقديرية من وزارة التربية والتعليم ومؤسسات تعليمية
                  دولية.
                </p>
              </div>
            </div>
            <div className="relative">
              <Image
                src="/placeholder.svg?height=400&width=500"
                alt="تاريخ المدرسة"
                width={500}
                height={400}
                className="rounded-xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
