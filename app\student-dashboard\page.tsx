import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function StudentDashboardPage() {
  const schedule = [
    { time: "8:00 - 8:45", subject: "اللغة العربية", teacher: "أ. فاطمة أحمد", room: "A101" },
    { time: "8:45 - 9:30", subject: "الرياضيات", teacher: "أ. محمد علي", room: "B205" },
    { time: "9:30 - 10:15", subject: "العلوم", teacher: "أ. سارة محمود", room: "C301" },
    { time: "10:15 - 10:30", subject: "استراحة", teacher: "", room: "" },
    { time: "10:30 - 11:15", subject: "اللغة الإنجليزية", teacher: "أ. أميرة حسن", room: "A102" },
  ]

  const grades = [
    { subject: "اللغة العربية", grade: 95, total: 100, status: "ممتاز" },
    { subject: "الرياضيات", grade: 88, total: 100, status: "جيد جداً" },
    { subject: "العلوم", grade: 92, total: 100, status: "ممتاز" },
    { subject: "اللغة الإنجليزية", grade: 85, total: 100, status: "جيد جداً" },
  ]

  const stages = [
    {
      name: "رياض الأطفال",
      description: "مرحلة تأسيسية للأطفال من سن 3-5 سنوات",
      classes: ["KG1", "KG2"],
      color: "bg-pink-500",
    },
    {
      name: "المرحلة الابتدائية",
      description: "الصفوف من الأول إلى السادس الابتدائي",
      classes: ["الأول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس"],
      color: "bg-blue-500",
    },
    {
      name: "المرحلة الإعدادية",
      description: "الصفوف من الأول إلى الثالث الإعدادي",
      classes: ["الأول الإعدادي", "الثاني الإعدادي", "الثالث الإعدادي"],
      color: "bg-green-500",
    },
  ]

  const getGradeColor = (grade: number) => {
    if (grade >= 95) return "text-green-600"
    if (grade >= 85) return "text-blue-600"
    if (grade >= 75) return "text-yellow-600"
    return "text-red-600"
  }

  const getStatusBadge = (status: string) => {
    const colors: { [key: string]: string } = {
      ممتاز: "bg-green-100 text-green-800",
      "جيد جداً": "bg-blue-100 text-blue-800",
      جيد: "bg-yellow-100 text-yellow-800",
      مقبول: "bg-orange-100 text-orange-800",
    }
    return colors[status] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-lg sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-20">
            <Link href="/" className="flex items-center space-x-4 space-x-reverse">
              <div className="w-[60px] h-[60px] bg-blue-900 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                  <path d="M22 10v6M2 10l10-5 10 5-10 5z"></path>
                  <path d="M6 12v5c0 2 2 3 6 3s6-1 6-3v-5"></path>
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-blue-900">مدرسة الجيل الواعد الخاصة</h1>
                <p className="text-sm text-gray-600">نحو مستقبل أفضل</p>
              </div>
            </Link>

            <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
              <Link href="/" className="text-gray-700 hover:text-blue-600">الرئيسية</Link>
              <Link href="/#about" className="text-gray-700 hover:text-blue-600">من نحن</Link>
              <Link href="/#news" className="text-gray-700 hover:text-blue-600">الأخبار</Link>
              <Link href="/events" className="text-gray-700 hover:text-blue-600">الفعاليات</Link>
              <Link href="/student-dashboard" className="text-blue-600 font-medium">لوحة الطالب</Link>
            </nav>

            <Button variant="ghost" size="icon" className="lg:hidden">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            </Button>
          </div>
        </div>
      </header>

      <div className="py-20">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font\
