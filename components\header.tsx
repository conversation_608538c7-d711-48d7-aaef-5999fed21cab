"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Menu, X, GraduationCap, Users, Calendar, Camera, Phone, BookOpen } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const menuItems = [
    { name: "الرئيسية", href: "/", icon: GraduationCap },
    { name: "من نحن", href: "/about", icon: Users },
    { name: "رؤيتنا ورسالتنا", href: "/vision", icon: BookOpen },
    { name: "الأخبار", href: "/news", icon: Calendar },
    { name: "معرض الصور", href: "/gallery", icon: Camera },
    { name: "الفعاليات", href: "/events", icon: Calendar },
    { name: "لوحة الطالب", href: "/student-dashboard", icon: Users },
    { name: "تواصل معنا", href: "/contact", icon: Phone },
  ]

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <Image
              src="/images/school-logo.png"
              alt="شعار مدرسة الجيل الواعد"
              width={60}
              height={60}
              className="rounded-full"
            />
            <div>
              <h1 className="text-xl font-bold text-blue-900">مدرسة الجيل الواعد الخاصة</h1>
              <p className="text-sm text-gray-600">نحو مستقبل أفضل</p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
            {menuItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-blue-600 transition-colors"
              >
                <item.icon className="w-4 h-4" />
                <span>{item.name}</span>
              </Link>
            ))}
          </nav>

          {/* Mobile Menu Button */}
          <Button variant="ghost" size="icon" className="lg:hidden" onClick={() => setIsMenuOpen(!isMenuOpen)}>
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t">
            <nav className="flex flex-col space-y-4">
              {menuItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-3 space-x-reverse text-gray-700 hover:text-blue-600 transition-colors p-2 rounded-lg hover:bg-gray-50"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
