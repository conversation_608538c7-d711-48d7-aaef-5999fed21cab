import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Calendar, Clock, MapPin, Users, Star } from "lucide-react"
import Image from "next/image"

export default function EventsSection() {
  const upcomingEvents = [
    {
      id: 1,
      title: "معرض العلوم السنوي",
      description: "معرض يعرض مشاريع الطلاب العلمية والابتكارية في مختلف المجالات",
      date: "2024-12-15",
      time: "10:00 ص - 4:00 م",
      location: "قاعة المدرسة الكبرى",
      attendees: 200,
      image: "/placeholder.svg?height=300&width=400",
      category: "تعليمي",
      status: "قريباً",
    },
    {
      id: 2,
      title: "حفل التميز الأكاديمي",
      description: "تكريم الطلاب المتفوقين والمعلمين المتميزين في العام الدراسي",
      date: "2024-12-20",
      time: "6:00 م - 9:00 م",
      location: "المسرح المدرسي",
      attendees: 300,
      image: "/placeholder.svg?height=300&width=400",
      category: "احتفالي",
      status: "قريباً",
    },
    {
      id: 3,
      title: "ورشة الروبوتيك للطلاب",
      description: "ورشة تدريبية لتعليم الطلاب أساسيات الروبوتيك والبرمجة",
      date: "2024-12-10",
      time: "9:00 ص - 12:00 م",
      location: "مختبر الحاسوب",
      attendees: 50,
      image: "/placeholder.svg?height=300&width=400",
      category: "تقني",
      status: "التسجيل مفتوح",
    },
  ]

  const pastEvents = [
    {
      id: 4,
      title: "يوم الرياضة المدرسي",
      description: "يوم مليء بالأنشطة الرياضية والمسابقات بين الطلاب",
      date: "2024-11-15",
      time: "8:00 ص - 2:00 م",
      location: "الملعب المدرسي",
      attendees: 400,
      image: "/placeholder.svg?height=300&width=400",
      category: "رياضي",
      status: "انتهى",
    },
    {
      id: 5,
      title: "مهرجان الثقافة والفنون",
      description: "عروض فنية وثقافية متنوعة من إبداع طلاب المدرسة",
      date: "2024-11-01",
      time: "5:00 م - 8:00 م",
      location: "المسرح المدرسي",
      attendees: 250,
      image: "/placeholder.svg?height=300&width=400",
      category: "ثقافي",
      status: "انتهى",
    },
    {
      id: 6,
      title: "ورشة تدريب المعلمين",
      description: "ورشة تدريبية للمعلمين حول أحدث طرق التدريس",
      date: "2024-10-20",
      time: "9:00 ص - 3:00 م",
      location: "قاعة الاجتماعات",
      attendees: 30,
      image: "/placeholder.svg?height=300&width=400",
      category: "تطوير مهني",
      status: "انتهى",
    },
  ]

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      تعليمي: "bg-blue-500",
      احتفالي: "bg-purple-500",
      تقني: "bg-green-500",
      رياضي: "bg-orange-500",
      ثقافي: "bg-pink-500",
      "تطوير مهني": "bg-indigo-500",
    }
    return colors[category] || "bg-gray-500"
  }

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      قريباً: "bg-yellow-100 text-yellow-800",
      "التسجيل مفتوح": "bg-green-100 text-green-800",
      انتهى: "bg-gray-100 text-gray-800",
    }
    return colors[status] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="py-20">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">فعاليات المدرسة</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            تابع جميع الفعاليات والأنشطة المدرسية التي تهدف إلى إثراء تجربة الطلاب التعليمية والثقافية
          </p>
        </div>

        {/* Upcoming Events */}
        <div className="mb-20">
          <div className="flex items-center gap-4 mb-12">
            <div className="bg-blue-500 rounded-full p-3">
              <Calendar className="w-6 h-6 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900">الفعاليات القادمة</h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {upcomingEvents.map((event) => (
              <Card
                key={event.id}
                className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg overflow-hidden"
              >
                <div className="relative overflow-hidden">
                  <Image
                    src={event.image || "/placeholder.svg"}
                    alt={event.title}
                    width={400}
                    height={300}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 right-4 flex gap-2">
                    <span
                      className={`${getCategoryColor(event.category)} text-white px-3 py-1 rounded-full text-sm font-medium`}
                    >
                      {event.category}
                    </span>
                    <span className={`${getStatusColor(event.status)} px-3 py-1 rounded-full text-sm font-medium`}>
                      {event.status}
                    </span>
                  </div>
                  {event.status === "قريباً" && (
                    <div className="absolute top-4 left-4">
                      <Star className="w-6 h-6 text-yellow-400 fill-current" />
                    </div>
                  )}
                </div>

                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                    {event.title}
                  </h3>

                  <p className="text-gray-600 mb-4 leading-relaxed">{event.description}</p>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center gap-3 text-sm text-gray-500">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(event.date).toLocaleDateString("ar-EG")}</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm text-gray-500">
                      <Clock className="w-4 h-4" />
                      <span>{event.time}</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm text-gray-500">
                      <MapPin className="w-4 h-4" />
                      <span>{event.location}</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm text-gray-500">
                      <Users className="w-4 h-4" />
                      <span>{event.attendees} مشارك متوقع</span>
                    </div>
                  </div>

                  <Button className="w-full bg-blue-600 hover:bg-blue-700">
                    {event.status === "التسجيل مفتوح" ? "سجل الآن" : "المزيد من التفاصيل"}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Past Events */}
        <div>
          <div className="flex items-center gap-4 mb-12">
            <div className="bg-gray-500 rounded-full p-3">
              <Calendar className="w-6 h-6 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900">الفعاليات السابقة</h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {pastEvents.map((event) => (
              <Card
                key={event.id}
                className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden opacity-90"
              >
                <div className="relative overflow-hidden">
                  <Image
                    src={event.image || "/placeholder.svg"}
                    alt={event.title}
                    width={400}
                    height={300}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 right-4 flex gap-2">
                    <span
                      className={`${getCategoryColor(event.category)} text-white px-3 py-1 rounded-full text-sm font-medium`}
                    >
                      {event.category}
                    </span>
                    <span className={`${getStatusColor(event.status)} px-3 py-1 rounded-full text-sm font-medium`}>
                      {event.status}
                    </span>
                  </div>
                </div>

                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{event.title}</h3>

                  <p className="text-gray-600 mb-4 leading-relaxed">{event.description}</p>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center gap-3 text-sm text-gray-500">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(event.date).toLocaleDateString("ar-EG")}</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm text-gray-500">
                      <Clock className="w-4 h-4" />
                      <span>{event.time}</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm text-gray-500">
                      <MapPin className="w-4 h-4" />
                      <span>{event.location}</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm text-gray-500">
                      <Users className="w-4 h-4" />
                      <span>{event.attendees} مشارك</span>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    عرض الصور والتقرير
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
