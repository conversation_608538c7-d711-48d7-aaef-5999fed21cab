import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { GraduationCap, Users, BookOpen, Award, Calendar, Camera, Phone } from "lucide-react"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-lg sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-[60px] h-[60px] relative">
                <Image
                  src="/images/school-logo.png"
                  alt="شعار مدرسة الجيل الواعد"
                  fill
                  className="rounded-full object-contain"
                />
              </div>
              <div>
                <h1 className="text-xl font-bold text-blue-900">مدرسة الجيل الواعد الخاصة</h1>
                <p className="text-sm text-gray-600">نحو مستقبل أفضل</p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
              <Link href="/" className="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-blue-600">
                <GraduationCap className="w-4 h-4" />
                <span>الرئيسية</span>
              </Link>
              <Link
                href="#about"
                className="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-blue-600"
              >
                <Users className="w-4 h-4" />
                <span>من نحن</span>
              </Link>
              <Link
                href="#news"
                className="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-blue-600"
              >
                <Calendar className="w-4 h-4" />
                <span>الأخبار</span>
              </Link>
              <Link
                href="#gallery"
                className="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-blue-600"
              >
                <Camera className="w-4 h-4" />
                <span>معرض الصور</span>
              </Link>
              <Link
                href="#contact"
                className="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-blue-600"
              >
                <Phone className="w-4 h-4" />
                <span>تواصل معنا</span>
              </Link>
            </nav>

            {/* Mobile Menu Button */}
            <Button variant="ghost" size="icon" className="lg:hidden">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                  مرحباً بكم في
                  <span className="block text-yellow-400">مدرسة الجيل الواعد</span>
                </h1>
                <p className="text-xl text-blue-100 leading-relaxed">
                  نحن نؤمن بأن التعليم هو مفتاح المستقبل، ونسعى لتقديم تعليم متميز يبني جيلاً واعداً قادراً على مواجهة
                  تحديات المستقبل
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold">
                  تعرف على المدرسة
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white bg-transparent hover:bg-white hover:text-blue-900"
                >
                  تواصل معنا
                </Button>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-8">
                <div className="text-center">
                  <div className="bg-white/10 rounded-full p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                    <GraduationCap className="w-8 h-8" />
                  </div>
                  <div className="text-2xl font-bold">1000+</div>
                  <div className="text-sm text-blue-200">طالب وطالبة</div>
                </div>
                <div className="text-center">
                  <div className="bg-white/10 rounded-full p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                    <Users className="w-8 h-8" />
                  </div>
                  <div className="text-2xl font-bold">70+</div>
                  <div className="text-sm text-blue-200">معلم ومعلمة</div>
                </div>
                <div className="text-center">
                  <div className="bg-white/10 rounded-full p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                    <BookOpen className="w-8 h-8" />
                  </div>
                  <div className="text-2xl font-bold">15+</div>
                  <div className="text-sm text-blue-200">سنة خبرة</div>
                </div>
                <div className="text-center">
                  <div className="bg-white/10 rounded-full p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                    <Award className="w-8 h-8" />
                  </div>
                  <div className="text-2xl font-bold">20+</div>
                  <div className="text-sm text-blue-200">جائزة تميز</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative z-10 h-[400px] w-full">
                <Image
                  src="/images/graduation-ceremony.jpg"
                  alt="حفل تخرج طلاب مدرسة الجيل الواعد"
                  fill
                  className="rounded-2xl object-cover"
                  priority
                />
              </div>
              <div className="absolute -top-4 -right-4 w-full h-full bg-yellow-400 rounded-2xl -z-10"></div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">من نحن</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              مدرسة الجيل الواعد الخاصة هي مؤسسة تعليمية رائدة تأسست بهدف تقديم تعليم متميز يواكب التطورات الحديثة ويعد
              جيلاً واعداً قادراً على بناء المستقبل
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: GraduationCap,
                title: "رؤيتنا للطلاب",
                description: "تقديم بيئة تعليمية حديثة وتطوير مهارات التفكير والنقد مع الاهتمام بالأنشطة والابتكار",
                color: "bg-blue-500",
              },
              {
                icon: Users,
                title: "رؤيتنا للمعلمين",
                description: "التميز في الأداء التربوي والتطوير المهني المستمر مع استخدام تقنيات حديثة في التدريس",
                color: "bg-green-500",
              },
              {
                icon: BookOpen,
                title: "رؤيتنا لأولياء الأمور",
                description: "شراكة حقيقية في العملية التعليمية مع تواصل دائم وشفاف وإشراك في الأنشطة والقرارات",
                color: "bg-purple-500",
              },
              {
                icon: Award,
                title: "رؤيتنا للإدارة",
                description: "نظام إداري فعال ومرن مع شفافية في اتخاذ القرار وتطبيق الجودة الإدارية والتحول الرقمي",
                color: "bg-orange-500",
              },
            ].map((feature, index) => (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                <CardContent className="p-8 text-center">
                  <div
                    className={`${feature.color} rounded-full p-4 w-20 h-20 mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                  >
                    <feature.icon className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* News Section */}
      <section id="news" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">أحدث الأخبار</h2>
            <p className="text-xl text-gray-600">تابع آخر أخبار وأنشطة المدرسة</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                id: 1,
                title: "بدء العام الدراسي الجديد 2024-2025",
                excerpt: "نرحب بجميع الطلاب والطالبات في العام الدراسي الجديد مع تطبيق أحدث المناهج التعليمية",
                date: "2024-09-01",
                time: "10:00 ص",
                category: "إعلانات",
              },
              {
                id: 2,
                title: "فوز طلابنا في مسابقة العلوم الوطنية",
                excerpt: "حقق طلاب المدرسة المركز الأول في مسابقة العلوم على مستوى المحافظة",
                date: "2024-08-25",
                time: "2:30 م",
                category: "إنجازات",
              },
              {
                id: 3,
                title: "ورشة تدريبية للمعلمين حول التعلم الرقمي",
                excerpt: "تم تنظيم ورشة تدريبية متخصصة لتطوير مهارات المعلمين في استخدام التكنولوجيا",
                date: "2024-08-20",
                time: "9:00 ص",
                category: "تطوير",
              },
            ].map((item) => (
              <Card
                key={item.id}
                className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden"
              >
                <div className="relative overflow-hidden h-48 bg-gray-200 flex items-center justify-center">
                  <Calendar className="w-12 h-12 text-gray-400" />
                  <div className="absolute top-4 right-4">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {item.category}
                    </span>
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>{item.date}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                      </svg>
                      <span>{item.time}</span>
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                    {item.title}
                  </h3>

                  <p className="text-gray-600 mb-4 leading-relaxed">{item.excerpt}</p>

                  <Button variant="ghost" className="p-0 h-auto font-semibold text-blue-600 hover:text-blue-700">
                    اقرأ المزيد
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2"
                    >
                      <line x1="19" y1="12" x2="5" y2="12"></line>
                      <polyline points="12 19 5 12 12 5"></polyline>
                    </svg>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              عرض جميع الأخبار
            </Button>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section id="gallery" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">معرض الصور والفيديوهات</h2>
            <p className="text-xl text-gray-600">شاهد لحظات مميزة من حياة المدرسة</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { title: "حفل التخرج السنوي", category: "فعاليات", image: "/images/graduation-ceremony.jpg" },
              { title: "الفصول الدراسية الحديثة", category: "مرافق" },
              { title: "مختبر العلوم المتطور", category: "مرافق" },
              { title: "نشاط رياضي للطلاب", category: "أنشطة" },
              { title: "ورشة تعليمية", category: "أنشطة" },
              { title: "المكتبة المدرسية", category: "مرافق" },
            ].map((photo, index) => (
              <Card
                key={index}
                className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden"
              >
                <div className="relative overflow-hidden h-64 bg-gray-200 flex items-center justify-center">
                  {photo.image ? (
                    <Image
                      src={photo.image}
                      alt={photo.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <Camera className="w-12 h-12 text-gray-400" />
                  )}
                  <div className="absolute top-4 right-4">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {photo.category}
                    </span>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-gray-900">{photo.title}</h3>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">تواصل معنا</h2>
            <p className="text-xl text-gray-600">نحن هنا للإجابة على جميع استفساراتكم</p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="shadow-xl border-0">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">أرسل لنا رسالة</h3>
                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                      <input
                        type="text"
                        placeholder="أدخل اسمك الكامل"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                      <input
                        type="tel"
                        placeholder="أدخل رقم هاتفك"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input
                      type="email"
                      placeholder="أدخل بريدك الإلكتروني"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الموضوع</label>
                    <input
                      type="text"
                      placeholder="موضوع الرسالة"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الرسالة</label>
                    <textarea
                      placeholder="اكتب رسالتك هنا..."
                      rows={5}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    ></textarea>
                  </div>
                  <Button size="lg" className="w-full bg-blue-600 hover:bg-blue-700">
                    إرسال الرسالة
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <div className="space-y-8">
              <Card className="shadow-lg border-0">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-blue-500 rounded-full p-3">
                      <Phone className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">الهاتف</h4>
                      <p className="text-gray-600">+20 12 04452332</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-green-500 rounded-full p-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-white"
                      >
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">البريد الإلكتروني</h4>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-purple-500 rounded-full p-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-white"
                      >
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                        <circle cx="12" cy="10" r="3"></circle>
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">العنوان</h4>
                      <p className="text-gray-600">شارع الصفا والمروة، طوابق، فيصل، الجيزة</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* School Info */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="w-[50px] h-[50px] relative">
                  <Image
                    src="/images/school-logo.png"
                    alt="شعار المدرسة"
                    fill
                    className="rounded-full object-contain"
                  />
                </div>
                <div>
                  <h3 className="text-xl font-bold">مدرسة الجيل الواعد</h3>
                  <p className="text-gray-400 text-sm">نحو مستقبل أفضل</p>
                </div>
              </div>
              <p className="text-gray-300 leading-relaxed">
                مؤسسة تعليمية رائدة تسعى لتقديم تعليم متميز يواكب التطورات الحديثة ويعد جيلاً واعداً قادراً على بناء
                المستقبل.
              </p>
              <div className="flex space-x-4 space-x-reverse">
                <Link href="#" className="text-gray-400 hover:text-blue-400 transition-colors">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                  </svg>
                </Link>
                <Link href="#" className="text-gray-400 hover:text-blue-400 transition-colors">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                  </svg>
                </Link>
                <Link href="#" className="text-gray-400 hover:text-pink-400 transition-colors">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                  </svg>
                </Link>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-6">روابط سريعة</h4>
              <ul className="space-y-3">
                <li>
                  <Link href="/" className="text-gray-300 hover:text-white transition-colors">
                    الرئيسية
                  </Link>
                </li>
                <li>
                  <Link href="#about" className="text-gray-300 hover:text-white transition-colors">
                    من نحن
                  </Link>
                </li>
                <li>
                  <Link href="#news" className="text-gray-300 hover:text-white transition-colors">
                    الأخبار
                  </Link>
                </li>
                <li>
                  <Link href="#gallery" className="text-gray-300 hover:text-white transition-colors">
                    معرض الصور
                  </Link>
                </li>
                <li>
                  <Link href="#contact" className="text-gray-300 hover:text-white transition-colors">
                    تواصل معنا
                  </Link>
                </li>
              </ul>
            </div>

            {/* Academic */}
            <div>
              <h4 className="text-lg font-semibold mb-6">الأقسام الأكاديمية</h4>
              <ul className="space-y-3">
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white transition-colors">
                    رياض الأطفال
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white transition-colors">
                    المرحلة الابتدائية
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white transition-colors">
                    المرحلة الإعدادية
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white transition-colors">
                    لوحة الطالب
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="text-lg font-semibold mb-6">معلومات التواصل</h4>
              <div className="space-y-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <Phone className="w-5 h-5 text-blue-400" />
                  <span className="text-gray-300">+20 12 04452332</span>
                </div>
                <div className="flex items-center space-x-3 space-x-reverse">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-green-400"
                  >
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <span className="text-gray-300"><EMAIL></span>
                </div>
                <div className="flex items-start space-x-3 space-x-reverse">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-purple-400 mt-1"
                  >
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>
                  <span className="text-gray-300">شارع الصفا والمروة، طوابق، فيصل، الجيزة</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800">
          <div className="container mx-auto px-4 py-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">© 2024 مدرسة الجيل الواعد الخاصة. جميع الحقوق محفوظة.</p>
              <div className="flex space-x-6 space-x-reverse mt-4 md:mt-0">
                <Link href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
                  سياسة الخصوصية
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
                  شروط الاستخدام
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
