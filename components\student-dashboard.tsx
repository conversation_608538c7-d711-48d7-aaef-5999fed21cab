import { Card, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, BookOpen, Trophy, Clock, User, GraduationCap } from "lucide-react"

export default function StudentDashboard() {
  const schedule = [
    { time: "8:00 - 8:45", subject: "اللغة العربية", teacher: "أ. فاطمة أحمد", room: "A101" },
    { time: "8:45 - 9:30", subject: "الرياضيات", teacher: "أ. محمد علي", room: "B205" },
    { time: "9:30 - 10:15", subject: "العلوم", teacher: "أ. سارة محمود", room: "C301" },
    { time: "10:15 - 10:30", subject: "استراحة", teacher: "", room: "" },
    { time: "10:30 - 11:15", subject: "اللغة الإنجليزية", teacher: "أ. أميرة حسن", room: "A102" },
    { time: "11:15 - 12:00", subject: "التربية الإسلامية", teacher: "أ. عبد الله يوسف", room: "B201" },
    { time: "12:00 - 12:45", subject: "الدراسات الاجتماعية", teacher: "أ. نادية عثمان", room: "A103" },
  ]

  const grades = [
    { subject: "اللغة العربية", grade: 95, total: 100, status: "ممتاز" },
    { subject: "الرياضيات", grade: 88, total: 100, status: "جيد جداً" },
    { subject: "العلوم", grade: 92, total: 100, status: "ممتاز" },
    { subject: "اللغة الإنجليزية", grade: 85, total: 100, status: "جيد جداً" },
    { subject: "التربية الإسلامية", grade: 98, total: 100, status: "ممتاز" },
    { subject: "الدراسات الاجتماعية", grade: 90, total: 100, status: "ممتاز" },
  ]

  const stages = [
    {
      name: "رياض الأطفال",
      description: "مرحلة تأسيسية للأطفال من سن 3-5 سنوات",
      classes: ["KG1", "KG2"],
      color: "bg-pink-500",
    },
    {
      name: "المرحلة الابتدائية",
      description: "الصفوف من الأول إلى السادس الابتدائي",
      classes: ["الأول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس"],
      color: "bg-blue-500",
    },
    {
      name: "المرحلة الإعدادية",
      description: "الصفوف من الأول إلى الثالث الإعدادي",
      classes: ["الأول الإعدادي", "الثاني الإعدادي", "الثالث الإعدادي"],
      color: "bg-green-500",
    },
  ]

  const getGradeColor = (grade: number) => {
    if (grade >= 95) return "text-green-600"
    if (grade >= 85) return "text-blue-600"
    if (grade >= 75) return "text-yellow-600"
    return "text-red-600"
  }

  const getStatusBadge = (status: string) => {
    const colors: { [key: string]: string } = {
      ممتاز: "bg-green-100 text-green-800",
      "جيد جداً": "bg-blue-100 text-blue-800",
      جيد: "bg-yellow-100 text-yellow-800",
      مقبول: "bg-orange-100 text-orange-800",
    }
    return colors[status] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="py-20">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">لوحة الطالب</h1>
          <p className="text-xl text-gray-600">متابعة الجدول الدراسي والنتائج والمراحل التعليمية</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Schedule */}
          <div className="lg:col-span-2">
            <Card className="shadow-xl border-0 mb-8">
              <CardHeader className="bg-blue-50">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <Calendar className="w-6 h-6 text-blue-600" />
                  الجدول الدراسي الأسبوعي
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-4 text-right font-semibold text-gray-900">الوقت</th>
                        <th className="px-6 py-4 text-right font-semibold text-gray-900">المادة</th>
                        <th className="px-6 py-4 text-right font-semibold text-gray-900">المعلم</th>
                        <th className="px-6 py-4 text-right font-semibold text-gray-900">القاعة</th>
                      </tr>
                    </thead>
                    <tbody>
                      {schedule.map((item, index) => (
                        <tr
                          key={index}
                          className={`border-b hover:bg-gray-50 ${item.subject === "استراحة" ? "bg-yellow-50" : ""}`}
                        >
                          <td className="px-6 py-4 font-medium text-gray-900">
                            <div className="flex items-center gap-2">
                              <Clock className="w-4 h-4 text-gray-500" />
                              {item.time}
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center gap-2">
                              <BookOpen className="w-4 h-4 text-blue-500" />
                              <span className="font-medium">{item.subject}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-gray-600">
                            {item.teacher && (
                              <div className="flex items-center gap-2">
                                <User className="w-4 h-4 text-gray-400" />
                                {item.teacher}
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-4 text-gray-600">{item.room}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Grades */}
            <Card className="shadow-xl border-0">
              <CardHeader className="bg-green-50">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <Trophy className="w-6 h-6 text-green-600" />
                  نتائج الامتحانات
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  {grades.map((grade, index) => (
                    <div key={index} className="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex justify-between items-start mb-3">
                        <h4 className="font-semibold text-gray-900">{grade.subject}</h4>
                        <Badge className={getStatusBadge(grade.status)}>{grade.status}</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className={`text-2xl font-bold ${getGradeColor(grade.grade)}`}>{grade.grade}</span>
                        <span className="text-gray-500">/ {grade.total}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                        <div
                          className={`h-2 rounded-full ${grade.grade >= 95 ? "bg-green-500" : grade.grade >= 85 ? "bg-blue-500" : "bg-yellow-500"}`}
                          style={{ width: `${grade.grade}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-semibold text-gray-900">المعدل العام:</span>
                    <span className="text-2xl font-bold text-blue-600">
                      {Math.round(grades.reduce((sum, grade) => sum + grade.grade, 0) / grades.length)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Academic Stages */}
          <div>
            <Card className="shadow-xl border-0">
              <CardHeader className="bg-purple-50">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <GraduationCap className="w-6 h-6 text-purple-600" />
                  المراحل الدراسية
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  {stages.map((stage, index) => (
                    <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start gap-3 mb-3">
                        <div className={`${stage.color} rounded-full p-2`}>
                          <GraduationCap className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 mb-1">{stage.name}</h4>
                          <p className="text-sm text-gray-600 mb-3">{stage.description}</p>
                          <div className="flex flex-wrap gap-2">
                            {stage.classes.map((className, classIndex) => (
                              <Badge key={classIndex} variant="outline" className="text-xs">
                                {className}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 pt-6 border-t">
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">عرض التفاصيل الكاملة</Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="shadow-xl border-0 mt-6">
              <CardHeader>
                <CardTitle className="text-lg">إحصائيات سريعة</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">إجمالي المواد:</span>
                    <span className="font-semibold">{grades.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">المواد الممتازة:</span>
                    <span className="font-semibold text-green-600">
                      {grades.filter((g) => g.status === "ممتاز").length}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">أعلى درجة:</span>
                    <span className="font-semibold text-blue-600">{Math.max(...grades.map((g) => g.grade))}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">الحضور:</span>
                    <span className="font-semibold text-green-600">95%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
