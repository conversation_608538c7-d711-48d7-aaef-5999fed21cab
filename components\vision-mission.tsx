import { Card, CardContent } from "@/components/ui/card"
import { Eye, Target, Compass } from "lucide-react"

export default function VisionMission() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">رؤيتنا ورسالتنا</h2>
          <p className="text-xl text-gray-600">نحو مستقبل تعليمي أفضل</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
            <CardContent className="p-8 text-center">
              <div className="bg-blue-500 rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Eye className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-blue-900 mb-6">رؤيتنا</h3>
              <p className="text-gray-700 leading-relaxed text-lg">
                أن نكون المدرسة الرائدة في تقديم تعليم متميز يجمع بين الأصالة والمعاصرة، ونساهم في إعداد جيل واعٍ ومبدع
                قادر على مواجهة تحديات المستقبل وبناء مجتمع متقدم.
              </p>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
            <CardContent className="p-8 text-center">
              <div className="bg-green-500 rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Target className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-green-900 mb-6">رسالتنا</h3>
              <p className="text-gray-700 leading-relaxed text-lg">
                نلتزم بتقديم تعليم عالي الجودة في بيئة تربوية محفزة للإبداع والتميز، مع التركيز على تنمية شخصية الطالب
                المتكاملة وإعداده ليكون مواطناً صالحاً ومنتجاً.
              </p>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-2xl transition-all duration-300 border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
            <CardContent className="p-8 text-center">
              <div className="bg-purple-500 rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Compass className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-purple-900 mb-6">قيمنا</h3>
              <div className="text-gray-700 leading-relaxed text-lg space-y-2">
                <p>• التميز في التعليم</p>
                <p>• الإبداع والابتكار</p>
                <p>• الشراكة المجتمعية</p>
                <p>• الشفافية والمصداقية</p>
                <p>• التطوير المستمر</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
