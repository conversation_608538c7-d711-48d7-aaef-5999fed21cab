import { Button } from "@/components/ui/button"
import { GraduationCap, Users, BookOpen, Award } from "lucide-react"
import Image from "next/image"

export default function Hero() {
  return (
    <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                مرحباً بكم في
                <span className="block text-yellow-400">مدرسة الجيل الواعد</span>
              </h1>
              <p className="text-xl text-blue-100 leading-relaxed">
                نحن نؤمن بأن التعليم هو مفتاح المستقبل، ونسعى لتقديم تعليم متميز يبني جيلاً واعداً قادراً على مواجهة تحديات
                المستقبل
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold">
                تعرف على المدرسة
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-900"
              >
                تواصل معنا
              </Button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-8">
              <div className="text-center">
                <div className="bg-white/10 rounded-full p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                  <GraduationCap className="w-8 h-8" />
                </div>
                <div className="text-2xl font-bold">500+</div>
                <div className="text-sm text-blue-200">طالب وطالبة</div>
              </div>
              <div className="text-center">
                <div className="bg-white/10 rounded-full p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                  <Users className="w-8 h-8" />
                </div>
                <div className="text-2xl font-bold">50+</div>
                <div className="text-sm text-blue-200">معلم ومعلمة</div>
              </div>
              <div className="text-center">
                <div className="bg-white/10 rounded-full p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                  <BookOpen className="w-8 h-8" />
                </div>
                <div className="text-2xl font-bold">15+</div>
                <div className="text-sm text-blue-200">سنة خبرة</div>
              </div>
              <div className="text-center">
                <div className="bg-white/10 rounded-full p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                  <Award className="w-8 h-8" />
                </div>
                <div className="text-2xl font-bold">20+</div>
                <div className="text-sm text-blue-200">جائزة تميز</div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative z-10">
              <Image
                src="/placeholder.svg?height=600&width=500"
                alt="طلاب مدرسة الجيل الواعد"
                width={500}
                height={600}
                className="rounded-2xl shadow-2xl"
              />
            </div>
            <div className="absolute -top-4 -right-4 w-full h-full bg-yellow-400 rounded-2xl -z-10"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
